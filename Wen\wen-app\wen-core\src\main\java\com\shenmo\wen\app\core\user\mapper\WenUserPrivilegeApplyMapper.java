package com.shenmo.wen.app.core.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeApply;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 用户特权申请流程Mapper接口
 * 
 * <AUTHOR>
 */
public interface WenUserPrivilegeApplyMapper extends BaseMapper<WenUserPrivilegeApply> {

    /**
     * 根据ID查询申请流程
     */
    default WenUserPrivilegeApply byId(Long id) {
        return selectById(id);
    }

    /**
     * 检查今日是否已申请过
     */
    default boolean existsTodayApplication(Long userId, Long privilegeId) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
        
        LambdaQueryWrapper<WenUserPrivilegeApply> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WenUserPrivilegeApply::getUserId, userId)
               .eq(WenUserPrivilegeApply::getPrivilegeId, privilegeId)
               .ge(WenUserPrivilegeApply::getCtTm, startOfDay)
               .le(WenUserPrivilegeApply::getCtTm, endOfDay);
        
        return selectCount(wrapper) > 0;
    }

    /**
     * 查询过期的申请流程
     */
    default List<WenUserPrivilegeApply> listExpired() {
        long currentTime = System.currentTimeMillis();
        LambdaQueryWrapper<WenUserPrivilegeApply> wrapper = Wrappers.lambdaQuery();
        wrapper.lt(WenUserPrivilegeApply::getExpireTime, currentTime)
               .in(WenUserPrivilegeApply::getStatus, 0); // 只查询进行中的
        
        return selectList(wrapper);
    }
}
