{"groups": [{"name": "article", "type": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "privilege.apply", "type": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties"}], "properties": [{"name": "article.check-detail-view", "type": "java.lang.String", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "article.content-max-length", "type": "java.lang.Integer", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "privilege.apply.apply-url-prefix", "type": "java.lang.String", "description": "申请页面URL路径前缀", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties"}, {"name": "privilege.apply.daily-limit", "type": "java.lang.Integer", "description": "每日申请次数限制", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties"}, {"name": "privilege.apply.expire-minutes", "type": "java.lang.Integer", "description": "申请流程过期时间（分钟）", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties"}, {"name": "privilege.apply.step-timeout-minutes", "type": "java.lang.Integer", "description": "每步骤超时时间（分钟）", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties"}], "hints": [], "ignored": {"properties": []}}