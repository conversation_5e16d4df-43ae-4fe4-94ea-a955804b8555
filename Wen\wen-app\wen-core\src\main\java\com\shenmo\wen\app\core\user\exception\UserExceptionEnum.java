package com.shenmo.wen.app.core.user.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@ExceptionType(type = UserException.class, module = UserExceptionEnum.MODULE)
public enum UserExceptionEnum implements ExceptionEnum {

    /**
     * 用户不存在
     */
    USER_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "啪！人没了...")),

    /**
     * 用户特权已失效
     */
    USER_PRIVILEGE_EXPIRED(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "您的特权已失效，请重新获取并激活哈~")),

    /**
     * 用户特权类型不存在
     */
    USER_PRIVILEGE_TYPE_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "目前还未开放这个特权哦~")),

    /**
     * 今日已申请过该特权
     */
    PRIVILEGE_VERIFICATION_DAILY_LIMIT_EXCEEDED(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "今日已申请过了，明天再来吧~")),

    /**
     * 特权申请流程不存在
     */
    PRIVILEGE_VERIFICATION_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.NOT_FOUND, "别搞，根本没有这个申请流程！")),

    /**
     * 特权申请流程已过期
     */
    PRIVILEGE_VERIFICATION_EXPIRED(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "来晚了，申请流程已经没了")),

    /**
     * 特权申请流程状态错误
     */
    PRIVILEGE_VERIFICATION_STATUS_ERROR(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "申请流程状态错误")),

    /**
     * 二维码内容格式错误
     */
    QR_CODE_CONTENT_INVALID(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "不对，二维码不对")),

    /**
     * 申请内容为空
     */
    VERIFICATION_CONTENT_EMPTY(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "我申请内容去哪了？")),
    ;

    public static final String MODULE = "003";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    UserExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
