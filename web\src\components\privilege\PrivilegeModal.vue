<!--
  特权管理弹框组件

  功能说明：
  - 提供两个 Tab：激活码生成和模板创建
  - 激活码生成：根据用户、模板/付费、过期时间、申请类型生成激活码
  - 模板创建：创建新的特权模板
-->
<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    title="特权管理"
    :mask-closable="false"
    style="width: 600px"
  >
    <div class="privilege-modal">
      <NTabs v-model:value="activeTab" type="line" animated>
        <!-- 激活码生成 Tab -->
        <NTabPane name="code" tab="激活码">
          <div class="tab-content">
            <NForm
              ref="codeFormRef"
              :model="codeForm"
              :rules="codeRules"
              label-placement="left"
              label-width="100px"
            >
              <!-- 用户选择 -->
              <NFormItem label="用户" path="userId">
                <NSelect
                  v-model:value="codeForm.userId"
                  placeholder="搜索用户名"
                  filterable
                  remote
                  :options="userOptions"
                  :loading="userLoading"
                  @search="handleUserSearch"
                  clearable
                />
              </NFormItem>

              <!-- 特权类型选择 -->
              <NFormItem label="特权类型" path="privilegeType">
                <NRadioGroup v-model:value="codeForm.privilegeType">
                  <NRadio :value="0">模板</NRadio>
                  <NRadio :value="1">付费</NRadio>
                </NRadioGroup>
              </NFormItem>

              <!-- 模板选择（当选择模板时显示） -->
              <NFormItem v-if="codeForm.privilegeType === 0" label="模板" path="templateId">
                <NSelect
                  v-model:value="codeForm.templateId"
                  placeholder="搜索模板名称"
                  filterable
                  remote
                  :options="templateOptions"
                  :loading="templateLoading"
                  @search="handleTemplateSearch"
                  clearable
                />
              </NFormItem>

              <!-- 付费面额（当选择付费时显示） -->
              <NFormItem v-if="codeForm.privilegeType === 1" label="付费面额" path="amount">
                <NInputNumber
                  v-model:value="codeForm.amount"
                  placeholder="请输入付费面额"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </NFormItem>

              <!-- 过期时间 -->
              <NFormItem label="过期时间" path="expireTime">
                <NDatePicker
                  v-model:value="expireTimeValue"
                  type="datetime"
                  placeholder="选择过期时间"
                  style="width: 100%"
                />
              </NFormItem>

              <!-- 申请类型 -->
              <NFormItem label="申请类型" path="applyType">
                <NRadioGroup v-model:value="codeForm.applyType">
                  <NRadio :value="0">短信申请</NRadio>
                  <NRadio :value="1">二维码申请</NRadio>
                </NRadioGroup>
              </NFormItem>
            </NForm>

            <!-- 生成的激活码显示区域 -->
            <div v-if="generatedCode" class="generated-code-section">
              <NDivider>生成的激活码</NDivider>
              <div class="code-display">
                <NInput :value="generatedCode" readonly placeholder="激活码将在这里显示">
                  <template #suffix>
                    <NButton text @click="copyCode">
                      <template #icon>
                        <DocumentEdit16Filled />
                      </template>
                    </NButton>
                  </template>
                </NInput>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="modal-actions">
              <NButton @click="handleCancel" :disabled="codeGenerating">取消</NButton>
              <NButton type="primary" @click="handleGenerateCode" :loading="codeGenerating">
                CODE
              </NButton>
            </div>
          </div>
        </NTabPane>

        <!-- 模板创建 Tab -->
        <NTabPane name="template" tab="模板创建">
          <div class="tab-content">
            <NForm
              ref="templateFormRef"
              :model="templateForm"
              :rules="templateRules"
              label-placement="left"
              label-width="100px"
            >
              <!-- 特权名称 -->
              <NFormItem label="特权名称" path="name">
                <NInput v-model:value="templateForm.name" placeholder="请输入特权名称" />
              </NFormItem>

              <!-- 特权图标 -->
              <NFormItem label="特权图标" path="icon">
                <NUpload
                  :default-file-list="[]"
                  :max="1"
                  list-type="image-card"
                  @change="handleIconUpload"
                >
                  上传图标
                </NUpload>
              </NFormItem>

              <!-- 特权描述 -->
              <NFormItem label="特权描述" path="description">
                <NInput
                  v-model:value="templateForm.description"
                  type="textarea"
                  placeholder="请输入特权描述"
                  :rows="3"
                />
              </NFormItem>

              <!-- 特权链接 -->
              <NFormItem label="特权链接" path="link">
                <NInput v-model:value="templateForm.link" placeholder="请输入特权链接" />
              </NFormItem>

              <!-- 付费面额 -->
              <NFormItem label="付费面额" path="amount">
                <NInputNumber
                  v-model:value="templateForm.amount"
                  placeholder="请输入付费面额"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </NFormItem>

              <!-- 申请类型 -->
              <NFormItem label="申请类型" path="applyType">
                <NRadioGroup v-model:value="templateForm.applyType">
                  <NRadio :value="0">短信申请</NRadio>
                  <NRadio :value="1">二维码申请</NRadio>
                </NRadioGroup>
              </NFormItem>
            </NForm>

            <!-- 操作按钮 -->
            <div class="modal-actions">
              <NButton @click="handleCancel" :disabled="templateSaving">取消</NButton>
              <NButton type="primary" @click="handleSaveTemplate" :loading="templateSaving">
                创建
              </NButton>
            </div>
          </div>
        </NTabPane>
      </NTabs>
    </div>
  </NModal>
</template>

<script lang="ts" setup>
import {
  NModal,
  NTabs,
  NTabPane,
  NForm,
  NFormItem,
  NSelect,
  NRadioGroup,
  NRadio,
  NInputNumber,
  NDatePicker,
  NInput,
  NButton,
  NDivider,
  NUpload,
  useMessage,
  type FormInst,
  type FormRules,
  type SelectOption,
  type UploadFileInfo,
} from 'naive-ui'
import { ref, computed, reactive } from 'vue'

import privilegeApi, { privilegeTemplateApi } from '@/api/privilege'
import { DocumentEdit16Filled } from '@/icons'
import type { PrivilegeCodeReq } from '@/types/privilege/privilege-code-req.types'
import type { PrivilegeTemplateSaveReq } from '@/types/privilege/privilege-template-save-req.types'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const message = useMessage()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const activeTab = ref('code')
const codeFormRef = ref<FormInst>()
const templateFormRef = ref<FormInst>()

// 激活码生成表单
const codeForm = reactive<PrivilegeCodeReq>({
  userId: 0,
  privilegeType: 0,
  templateId: undefined,
  amount: undefined,
  expireTime: null,
  verificationType: 0,
})

// 日期选择器的值（单独管理）
const expireTimeValue = ref<number | null>(null)

// 模板创建表单
const templateForm = reactive<PrivilegeTemplateSaveReq>({
  name: '',
  icon: '',
  description: '',
  link: '',
  amount: 0,
  verificationType: 0,
})

// 状态管理
const codeGenerating = ref(false)
const templateSaving = ref(false)
const generatedCode = ref('')

// 用户搜索相关
const userOptions = ref<SelectOption[]>([])
const userLoading = ref(false)

// 模板搜索相关
const templateOptions = ref<SelectOption[]>([])
const templateLoading = ref(false)

// 表单验证规则
const codeRules: FormRules = {
  userId: { required: true, message: '请选择用户', type: 'number' },
  privilegeType: { required: true, message: '请选择特权类型', type: 'number' },
  templateId: {
    required: true,
    validator: (rule, value) => {
      if (codeForm.privilegeType === 0 && !value) {
        return new Error('请选择模板')
      }
      return true
    },
  },
  amount: {
    required: true,
    validator: (rule, value) => {
      if (codeForm.privilegeType === 1 && (!value || value <= 0)) {
        return new Error('请输入有效的付费面额')
      }
      return true
    },
  },
  expireTime: { required: true, message: '请选择过期时间' },
  applyType: { required: true, message: '请选择申请类型', type: 'number' },
}

const templateRules: FormRules = {
  name: { required: true, message: '请输入特权名称' },
  icon: { required: true, message: '请上传特权图标' },
  description: { required: true, message: '请输入特权描述' },
  link: { required: true, message: '请输入特权链接' },
  amount: { required: true, message: '请输入付费面额', type: 'number' },
  applyType: { required: true, message: '请选择申请类型', type: 'number' },
}

/**
 * 搜索用户
 */
const handleUserSearch = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }

  userLoading.value = true
  try {
    const response = await privilegeApi.searchUsers(query)
    if (response.success && response.data) {
      userOptions.value = response.data.map((user) => ({
        label: user.username,
        value: user.id,
        avatar: user.avatar,
      }))
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

/**
 * 搜索模板
 */
const handleTemplateSearch = async (query: string) => {
  if (!query) {
    templateOptions.value = []
    return
  }

  templateLoading.value = true
  try {
    const response = await privilegeApi.searchTemplates(query)
    if (response.success && response.data) {
      templateOptions.value = response.data.map((template) => ({
        label: template.name,
        value: template.id,
      }))
    }
  } catch (error) {
    console.error('搜索模板失败:', error)
  } finally {
    templateLoading.value = false
  }
}

/**
 * 生成激活码
 */
const handleGenerateCode = async () => {
  if (!codeFormRef.value) return

  try {
    await codeFormRef.value.validate()
  } catch {
    return
  }

  codeGenerating.value = true
  try {
    // 转换时间格式
    const formData = {
      ...codeForm,
      expireTime: expireTimeValue.value ? new Date(expireTimeValue.value).toISOString() : null,
    }

    const response = await privilegeApi.generatePrivilegeCode(formData)
    if (response.success && response.data) {
      generatedCode.value = response.data
      message.success('激活码生成成功！')
    } else {
      message.error('激活码生成失败')
    }
  } catch (error) {
    console.error('生成激活码失败:', error)
    message.error('生成激活码失败，请稍后重试')
  } finally {
    codeGenerating.value = false
  }
}

/**
 * 保存模板
 */
const handleSaveTemplate = async () => {
  if (!templateFormRef.value) return

  try {
    await templateFormRef.value.validate()
  } catch {
    return
  }

  templateSaving.value = true
  try {
    const response = await privilegeTemplateApi.savePrivilegeTemplate(templateForm)
    if (response.success) {
      message.success('模板创建成功！')
      visible.value = false
      emit('success')
      resetForms()
    } else {
      message.error('模板创建失败')
    }
  } catch (error) {
    console.error('保存模板失败:', error)
    message.error('保存模板失败，请稍后重试')
  } finally {
    templateSaving.value = false
  }
}

/**
 * 处理图标上传
 */
const handleIconUpload = (options: { fileList: UploadFileInfo[] }) => {
  if (options.fileList.length > 0) {
    const file = options.fileList[0]
    if (file.url) {
      templateForm.icon = file.url
    }
  } else {
    templateForm.icon = ''
  }
}

/**
 * 复制激活码
 */
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value)
    message.success('激活码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  visible.value = false
  resetForms()
}

/**
 * 重置表单
 */
const resetForms = () => {
  // 重置激活码表单
  Object.assign(codeForm, {
    userId: 0,
    privilegeType: 0,
    templateId: undefined,
    amount: undefined,
    expireTime: null,
    verificationType: 0,
  })

  // 重置日期选择器
  expireTimeValue.value = null

  // 重置模板表单
  Object.assign(templateForm, {
    name: '',
    icon: '',
    description: '',
    link: '',
    amount: 0,
    verificationType: 0,
  })

  generatedCode.value = ''
  activeTab.value = 'code'
}

/**
 * 打开弹框
 */
const open = () => {
  visible.value = true
}

/**
 * 关闭弹框
 */
const close = () => {
  visible.value = false
}

/**
 * 重置表单（别名方法，保持向后兼容）
 */
const reset = () => {
  resetForms()
}

// 暴露方法给父组件
defineExpose({
  open,
  close,
  reset,
  resetForms,
})
</script>

<style lang="scss" scoped>
.privilege-modal {
  .tab-content {
    padding: 20px 0;
  }

  .generated-code-section {
    margin: 20px 0;

    .code-display {
      margin-top: 12px;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
}
</style>
