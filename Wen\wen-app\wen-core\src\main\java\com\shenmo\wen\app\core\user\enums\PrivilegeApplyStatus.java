package com.shenmo.wen.app.core.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特权申请状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrivilegeApplyStatus {

    /**
     * 进行中
     */
    IN_PROGRESS(0, "进行中"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAILED(2, "失败"),

    /**
     * 超时
     */
    TIMEOUT(3, "超时");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static PrivilegeApplyStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrivilegeApplyStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
