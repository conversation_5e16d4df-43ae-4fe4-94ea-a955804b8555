package com.shenmo.wen.app.core.user.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

import com.shenmo.wen.app.core.user.exception.UserException;
import com.shenmo.wen.app.core.user.exception.UserExceptionEnum;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeMapper;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeTemplateMapper;
import com.shenmo.wen.app.core.user.pojo.domain.WenUserPrivilegeActivate;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilege;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeActivateReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeCodeReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeSearchReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeResp;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeService;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.JacksonUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

@Service
@RequiredArgsConstructor
public class WenUserPrivilegeServiceImpl implements WenUserPrivilegeService {

    private static final String USER_PRIVILEGE_CODE_KEY = "wen:user:privilege:code:";

    private final WenUserPrivilegeMapper mapper;
    private final WenUserMapper userMapper;
    private final WenUserPrivilegeTemplateMapper templateMapper;

    @Override
    public String code(WenUserPrivilegeCodeReq req) {
        // 通过用户ID查询用户
        final WenUser user = userMapper.byId(req.getUserId());
        AssertUtils.isNotNull(user, UserExceptionEnum.USER_NOT_EXISTS);

        String templateName = null;
        // 如果有模板ID，查询模板名称
        if (req.getTemplateId() != null) {
            final WenUserPrivilegeTemplate template = templateMapper.byId(req.getTemplateId());
            AssertUtils.isNotNull(template, UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
            templateName = template.getName();
        } else if (req.getPaidDenomination() == null || req.getPaidDenomination() <= 0) {
            // 既没有模板ID也没有付费面额，无效
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
        }

        final WenUserPrivilegeActivate activate = new WenUserPrivilegeActivate();
        activate.setUsername(user.getUsername());
        activate.setTemplateName(templateName);
        activate.setExpireTime(req.getExpireTime());
        activate.setPaidDenomination(req.getPaidDenomination());
        activate.setApplyType(req.getApplyType());
        return generateCode(activate);
    }

    @Override
    public Boolean activate(WenUserPrivilegeActivateReq req) {
        final long loginId = StpUtil.getLoginIdAsLong();
        AssertUtils.isTrue(userMapper.existsById(loginId), UserExceptionEnum.USER_NOT_EXISTS);
        // 从code解析出用户信息与特权模板信息
        final WenUserPrivilegeActivate activate = parseCode(req.getCode());
        // 通过用户名查询用户
        final WenUser user = userMapper.byUsername(activate.getUsername());
        if (user == null) {
            return false;
        }
        final Integer paidDenomination = activate.getPaidDenomination();
        Long templateId = null;

        // 如果有模板名称，查询模板
        if (activate.getTemplateName() != null) {
            final WenUserPrivilegeTemplate template = templateMapper.byName(activate.getTemplateName());
            if (template == null) {
                return false;
            }
            templateId = template.getId();
        } else if (paidDenomination == null || paidDenomination <= 0) {
            // 既没有模板也没有付费面额，无效
            return false;
        }

        // 通过用户查询特权
        final WenUserPrivilege privilege = mapper.byUserIdAndTemplateId(user.getId(), templateId);
        if (privilege == null || !Objects.equals(privilege.getTemplateId(), templateId)) {
            // 创建特权
            final WenUserPrivilege newPrivilege = new WenUserPrivilege();
            newPrivilege.setUserId(user.getId());
            newPrivilege.setTemplateId(templateId);
            newPrivilege.setExpireTime(activate.getExpireTime());
            newPrivilege.setApplyType(activate.getApplyType());
            newPrivilege.setPaidDenomination(paidDenomination);
            mapper.insert(newPrivilege);
        } else {
            // 更新特权
            privilege.setTemplateId(templateId);
            privilege.setExpireTime(activate.getExpireTime());
            privilege.setApplyType(activate.getApplyType());
            privilege.setPaidDenomination(paidDenomination);
            mapper.updateById(privilege);
        }
        // 返回特权
        return true;
    }

    @Override
    public List<WenUserPrivilegeResp> search(WenUserPrivilegeSearchReq req) {
        final long loginId = StpUtil.getLoginIdAsLong();
        final long currentTime = System.currentTimeMillis();
        // 验证用户存在
        AssertUtils.isTrue(userMapper.existsById(loginId), UserExceptionEnum.USER_NOT_EXISTS);
        // 查询用户的有效特权
        final List<WenUserPrivilege> userPrivileges = mapper.listValidByUserId(loginId, currentTime);
        AssertUtils.isNotEmpty(userPrivileges, UserExceptionEnum.USER_PRIVILEGE_EXPIRED);
        // 计算最大面额
        Integer maxDenomination = 0;
        // 查找付费面额的最大值
        for (WenUserPrivilege privilege : userPrivileges) {
            if (privilege.getPaidDenomination() != null && privilege.getPaidDenomination() > maxDenomination) {
                maxDenomination = privilege.getPaidDenomination();
            }
        }
        // 查找模板面额的最大值
        final List<Long> templateIds = userPrivileges.stream()
                .map(WenUserPrivilege::getTemplateId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (!templateIds.isEmpty()) {
            final List<WenUserPrivilegeTemplate> userTemplates = templateMapper.listByIds(templateIds);
            for (WenUserPrivilegeTemplate template : userTemplates) {
                if (template.getDenomination() != null && template.getDenomination() > maxDenomination) {
                    maxDenomination = template.getDenomination();
                }
            }
        }
        AssertUtils.isTrue(maxDenomination > 0, UserExceptionEnum.USER_PRIVILEGE_EXPIRED);
        // 根据最大面额查询所有符合条件的模板ID
        final List<Long> allTemplateIds = templateMapper.listIdsByLeDenomination(maxDenomination);
        if (allTemplateIds.isEmpty()) {
            return List.of();
        }
        // 批量查询所有相关的特权和模板信息
        final List<WenUserPrivilege> privileges = mapper.listByTemplateIds(allTemplateIds);
        final List<WenUserPrivilegeTemplate> templates = templateMapper.listByIds(allTemplateIds);
        // 创建模板ID到模板对象的映射，避免循环查询
        final Map<Long, WenUserPrivilegeTemplate> templateMap = templates.stream()
                .collect(Collectors.toMap(WenUserPrivilegeTemplate::getId, Function.identity()));
        // 转换为Resp，使用批量查询的结果
        return privileges.stream()
                .map(privilege -> toResp(privilege, templateMap))
                .collect(Collectors.toList());
    }

    /**
     * 将特权转换为Resp（使用模板映射，避免循环查询）
     *
     * @param privilege   特权
     * @param templateMap 模板映射
     * @return 特权Resp
     */
    private WenUserPrivilegeResp toResp(WenUserPrivilege privilege, Map<Long, WenUserPrivilegeTemplate> templateMap) {
        final WenUserPrivilegeResp resp = new WenUserPrivilegeResp();
        final WenUserPrivilegeTemplate template = templateMap.get(privilege.getTemplateId());
        if (template == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_EXPIRED);
        }
        resp.setId(privilege.getId());
        resp.setName(template.getName());
        resp.setIcon(template.getIcon());
        resp.setDescription(template.getDescription());
        resp.setExpireTime(privilege.getExpireTime());
        resp.setLink(template.getLink());
        resp.setApplyType(privilege.getApplyType());
        return resp;
    }

    /**
     * 解析激活码
     *
     * @param code 激活码
     * @return 激活码解析结果
     */
    private WenUserPrivilegeActivate parseCode(String code) {
        final String json = (String) SpringRedisUtils.get(USER_PRIVILEGE_CODE_KEY + code);
        if (json == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_EXPIRED);
        }
        SpringRedisUtils.delete(USER_PRIVILEGE_CODE_KEY + code);
        return JacksonUtils.fromJson(json, WenUserPrivilegeActivate.class);
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    /**
     * 生成激活码
     *
     * @param activate 激活码
     * @return 激活码
     */
    private String generateCode(WenUserPrivilegeActivate activate) {
        // 定义字符集：大写字母和数字
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        // 生成6组，每组4位字符
        for (int group = 0; group < 6; group++) {
            if (group > 0) {
                code.append("-");
            }
            // 每组生成4位字符
            for (int i = 0; i < 4; i++) {
                int index = random.nextInt(CHARACTERS.length());
                code.append(CHARACTERS.charAt(index));
            }
        }
        SpringRedisUtils.set(USER_PRIVILEGE_CODE_KEY + code, JacksonUtils.toJson(activate));
        return code.toString();
    }
}
