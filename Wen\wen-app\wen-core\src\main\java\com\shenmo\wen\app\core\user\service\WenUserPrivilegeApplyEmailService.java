package com.shenmo.wen.app.core.user.service;

import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

/**
 * 特权申请邮件服务接口
 * 
 * <AUTHOR>
 */
public interface WenUserPrivilegeApplyEmailService {

        /**
         * 发送短信验证邮件
         * 
         * @param userA    申请用户A
         * @param userB    验证用户B
         * @param template 特权模板
         * @param pageUrl  申请页面URL
         * @return 是否发送成功
         */
        boolean sendSmsApplyEmail(WenUser userA, WenUser userB,
                        WenUserPrivilegeTemplate template, String pageUrl);

        /**
         * 发送二维码验证邮件
         * 
         * @param userA    申请用户A
         * @param userB    验证用户B
         * @param template 特权模板
         * @param pageUrl  申请页面URL
         * @return 是否发送成功
         */
        boolean sendQrCodeApplyEmail(WenUser userA, WenUser userB,
                        WenUserPrivilegeTemplate template, String pageUrl);
}
