package com.shenmo.wen.app.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * 特权申请配置属性
 * 
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(PrivilegeApplyProperties.PREFIX)
public class PrivilegeApplyProperties {

    public static final String PREFIX = "privilege.apply";
    /**
     * 每日申请次数限制
     */
    private Integer dailyLimit = 1;

    /**
     * 申请流程过期时间（分钟）
     */
    private Integer expireMinutes = 5;

    /**
     * 每步骤超时时间（分钟）
     */
    private Integer stepTimeoutMinutes = 3;

    /**
     * 申请页面URL路径前缀
     */
    private String applyUrlPrefix = "/privilege/apply/";
}
