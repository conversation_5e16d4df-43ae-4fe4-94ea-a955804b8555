package com.shenmo.wen.app.core.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特权申请步骤枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrivilegeApplyStep {

    /**
     * 第一步
     */
    STEP_ONE(1, "第一步"),

    /**
     * 第二步
     */
    STEP_TWO(2, "第二步"),

    /**
     * 第三步
     */
    STEP_THREE(3, "第三步");

    private final Integer code;
    private final String description;

    /**
     * 根据步骤码获取枚举
     */
    public static PrivilegeApplyStep fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrivilegeApplyStep step : values()) {
            if (step.getCode().equals(code)) {
                return step;
            }
        }
        return null;
    }
}
