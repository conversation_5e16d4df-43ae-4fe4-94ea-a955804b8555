package com.shenmo.wen.app.core.user.pojo.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WenUserPrivilegeActivate {
    /**
     * 用户名
     */
    private String username;
    /**
     * 特权模板名称（纯付费特权时为NULL）
     */
    private String templateName;
    /**
     * 过期时间（毫秒）
     */
    private Long expireTime;
    /**
     * 付费面额
     */
    private Integer paidDenomination;
    /**
     * 申请类型
     */
    private Integer applyType;
}
