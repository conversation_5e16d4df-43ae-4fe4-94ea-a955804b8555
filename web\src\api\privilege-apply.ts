import api from '@/utils/api/api'
import { type ResponseData } from '@/types/api/response-data.types'
import type { PrivilegeApplyStartRequest } from '@/types/privilege-apply/privilege-apply-start-request.types'
import type { PrivilegeApplyResponse } from '@/types/privilege-apply/privilege-apply-response.types'
import type { PrivilegeApplySubmitRequest } from '@/types/privilege-apply/privilege-apply-submit-request.types'
import type { ApplyTimeInfoResponse } from '@/types/privilege-apply/apply-time-info-response.types'

/**
 * 用户特权申请相关API接口
 * 提供特权申请流程的完整功能
 */
const applyApi = {
  /** API基础路径 */
  URL: '/core/user-privilege-applies',

  /**
   * 创建特权申请流程
   * @param request 申请启动请求参数
   * @returns 返回申请流程信息
   */
  startApply: async (
    request: PrivilegeApplyStartRequest,
  ): Promise<ResponseData<PrivilegeApplyResponse>> => {
    const response = await api.post<ResponseData<PrivilegeApplyResponse>>(
      applyApi.URL,
      request,
    )
    return response.data
  },

  /**
   * 查询申请流程状态
   * @param id 申请流程ID
   * @returns 返回申请流程信息
   */
  applyStatus: async (id: number): Promise<ResponseData<PrivilegeApplyResponse>> => {
    const response = await api.get<ResponseData<PrivilegeApplyResponse>>(
      `${applyApi.URL}/${id}/status`,
    )
    return response.data
  },

  /**
   * 提交申请内容（步骤二）
   * @param id 申请流程ID
   * @param request 提交请求参数
   * @returns 返回是否成功
   */
  submitApplyContent: async (
    id: number,
    request: PrivilegeApplySubmitRequest,
  ): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(
      `${applyApi.URL}/${id}/content`,
      request,
    )
    return response.data
  },

  /**
   * 触发步骤三（用户B访问申请页面时调用）
   * @param id 申请流程ID
   * @returns 返回是否成功
   */
  triggerStepThree: async (id: number): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(`${applyApi.URL}/${id}/step-three`)
    return response.data
  },

  /**
   * 启动申请计时器（用户点击邮件链接访问页面时调用）
   * @param id 申请流程ID
   * @returns 返回时间信息
   */
  startApplyTimer: async (id: number): Promise<ResponseData<ApplyTimeInfoResponse>> => {
    const response = await api.post<ResponseData<ApplyTimeInfoResponse>>(
      `${applyApi.URL}/${id}/timer`,
    )
    return response.data
  },

  /**
   * 获取申请剩余时间
   * @param id 申请流程ID
   * @returns 返回时间信息
   */
  remainingTime: async (id: number): Promise<ResponseData<ApplyTimeInfoResponse>> => {
    const response = await api.get<ResponseData<ApplyTimeInfoResponse>>(
      `${applyApi.URL}/${id}/remaining-time`,
    )
    return response.data
  },
}

export default applyApi
