<template>
  <div ref="turnstileContainer" class="turnstile-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

import config from '@/config'
import logger from '@/utils/log/log'

// 全局状态，使用 window 对象确保在热重载时保持状态
declare global {
  interface Window {
    __TURNSTILE_SCRIPT_LOADING__?: boolean
    __TURNSTILE_SCRIPT_LOADED__?: boolean
  }
}

// 简化的脚本加载函数
const loadTurnstileScript = (): Promise<void> => {
  const SCRIPT_URL = 'https://challenges.cloudflare.com/turnstile/v0/api.js'

  // 如果已经加载完成
  if (window.__TURNSTILE_SCRIPT_LOADED__ && window.turnstile) {
    return Promise.resolve()
  }

  // 如果正在加载中
  if (window.__TURNSTILE_SCRIPT_LOADING__) {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.__TURNSTILE_SCRIPT_LOADED__ && window.turnstile) {
          clearInterval(checkInterval)
          resolve()
        }
      }, 100)
    })
  }

  // 检查是否已经存在 window.turnstile
  if (window.turnstile) {
    window.__TURNSTILE_SCRIPT_LOADED__ = true
    return Promise.resolve()
  }

  // 检查是否已经存在脚本标签
  const existingScript = document.querySelector(`script[src="${SCRIPT_URL}"]`)
  if (existingScript) {
    window.__TURNSTILE_SCRIPT_LOADING__ = true
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.turnstile) {
          clearInterval(checkInterval)
          window.__TURNSTILE_SCRIPT_LOADING__ = false
          window.__TURNSTILE_SCRIPT_LOADED__ = true
          resolve()
        }
      }, 100)
    })
  }

  // 创建新的脚本标签
  window.__TURNSTILE_SCRIPT_LOADING__ = true

  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = SCRIPT_URL
    script.async = true
    script.defer = true

    script.onload = () => {
      window.__TURNSTILE_SCRIPT_LOADING__ = false
      window.__TURNSTILE_SCRIPT_LOADED__ = true
      logger.debug('Turnstile script loaded successfully')
      resolve()
    }

    script.onerror = () => {
      window.__TURNSTILE_SCRIPT_LOADING__ = false
      logger.error('Failed to load Turnstile script')
      reject(new Error('Failed to load Turnstile script'))
    }

    document.head.appendChild(script)
  })
}

// Turnstile API 类型声明
declare global {
  interface Window {
    turnstile?: {
      render: (
        element: Element | string,
        options: {
          sitekey: string
          callback?: (token: string) => void
          'error-callback'?: () => void
          'expired-callback'?: () => void
          theme?: 'light' | 'dark' | 'auto'
          size?: 'normal' | 'compact'
          retry?: 'auto' | 'never'
          'retry-interval'?: number
          'refresh-expired'?: 'auto' | 'manual' | 'never'
          language?: string
          execution?: 'render' | 'execute'
          appearance?: 'always' | 'execute' | 'interaction-only'
          'response-field'?: boolean
          'response-field-name'?: string
          cData?: string
        },
      ) => string
      reset: (widgetId?: string) => void
      remove: (widgetId?: string) => void
      getResponse: (widgetId?: string) => string
      isExpired: (widgetId?: string) => boolean
    }
  }
}

interface Props {
  sitekey?: string
  theme?: 'light' | 'dark' | 'auto'
  size?: 'normal' | 'compact'
  retry?: 'auto' | 'never'
  retryInterval?: number
  refreshExpired?: 'auto' | 'manual' | 'never'
  language?: string
  execution?: 'render' | 'execute'
  appearance?: 'always' | 'execute' | 'interaction-only'
  responseField?: boolean
  responseFieldName?: string
  cData?: string
}

const props = withDefaults(defineProps<Props>(), {
  sitekey: () => config.turnstile.siteKey,
  theme: 'auto',
  size: 'normal',
  retry: 'auto',
  retryInterval: 8000,
  refreshExpired: 'auto',
  language: 'auto',
  execution: 'render',
  appearance: 'always',
  responseField: true,
  responseFieldName: 'cf-turnstile-response',
  cData: '',
})

const emit = defineEmits<{
  (e: 'success', token: string): void
  (e: 'error'): void
  (e: 'expired'): void
}>()

const turnstileContainer = ref<HTMLElement>()
let widgetId: string | null = null

// 渲染 Turnstile 组件
const renderTurnstile = async () => {
  try {
    await loadTurnstileScript()

    if (!window.turnstile || !turnstileContainer.value) {
      logger.error('Turnstile not available or container not found')
      return
    }

    // 如果已经有 widget，先移除
    if (widgetId) {
      window.turnstile.remove(widgetId)
      widgetId = null
    }

    const options = {
      sitekey: props.sitekey,
      callback: (token: string) => {
        logger.debug('Turnstile verification successful')
        emit('success', token)
      },
      'error-callback': () => {
        logger.error('Turnstile verification failed')
        emit('error')
      },
      'expired-callback': () => {
        logger.warn('Turnstile verification expired')
        emit('expired')
      },
      theme: props.theme,
      size: props.size,
      retry: props.retry,
      'retry-interval': props.retryInterval,
      'refresh-expired': props.refreshExpired,
      language: props.language,
      execution: props.execution,
      appearance: props.appearance,
      'response-field': props.responseField,
      'response-field-name': props.responseFieldName,
      cData: props.cData,
    }

    widgetId = window.turnstile.render(turnstileContainer.value, options)
    logger.debug('Turnstile widget rendered with ID:', widgetId)
  } catch (error) {
    logger.error('Error rendering Turnstile:', error as Error)
    emit('error')
  }
}

// 重置 Turnstile
const reset = () => {
  if (window.turnstile && widgetId) {
    window.turnstile.reset(widgetId)
    logger.debug('Turnstile widget reset')
  }
}

// 移除 Turnstile
const remove = () => {
  if (window.turnstile && widgetId) {
    window.turnstile.remove(widgetId)
    widgetId = null
    logger.debug('Turnstile widget removed')
  }
}

// 获取响应
const getResponse = (): string => {
  if (window.turnstile && widgetId) {
    return window.turnstile.getResponse(widgetId)
  }
  return ''
}

// 检查是否过期
const isExpired = (): boolean => {
  if (window.turnstile && widgetId) {
    return window.turnstile.isExpired(widgetId)
  }
  return false
}

// 暴露方法给父组件
defineExpose({
  reset,
  remove,
  getResponse,
  isExpired,
})

// 监听 sitekey 变化，重新渲染
watch(
  () => props.sitekey,
  () => {
    if (props.sitekey) {
      renderTurnstile()
    }
  },
)

onMounted(() => {
  if (props.sitekey) {
    renderTurnstile()
  }
})

onUnmounted(() => {
  remove()
})
</script>

<style lang="scss">
@use '@/styles/verification';
</style>
