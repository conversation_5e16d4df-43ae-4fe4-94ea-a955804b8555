package com.shenmo.wen.app.core.user.pojo.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户特权申请启动请求
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeApplyStartReq {

    /**
     * 特权ID
     */
    @NotNull(message = "特权ID不能为空")
    private Long privilegeId;

    /**
     * 申请类型：0-短信申请，1-二维码申请
     */
    @NotNull(message = "申请类型不能为空")
    private Integer applyType;

    /**
     * 二维码内容（当申请类型为二维码时必填）
     */
    private String qrcc;
}
