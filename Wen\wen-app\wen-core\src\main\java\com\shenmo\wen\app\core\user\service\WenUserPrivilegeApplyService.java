package com.shenmo.wen.app.core.user.service;

import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplyStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplySubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenApplyTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeApplyResp;

/**
 * 用户特权申请服务接口
 * 
 * <AUTHOR>
 */
public interface WenUserPrivilegeApplyService {

    /**
     * 启动特权申请流程
     * 
     * @param req 启动参数
     * @return 申请流程信息
     */
    WenUserPrivilegeApplyResp startApply(WenUserPrivilegeApplyStartReq req);

    /**
     * 查询申请流程状态
     *
     * @param applyId 申请流程ID
     * @return 申请流程信息
     */
    WenUserPrivilegeApplyResp applyStatus(Long applyId);

    /**
     * 提交申请内容（步骤二）
     *
     * @param id 申请流程ID
     * @param req 提交参数
     * @return 是否成功
     */
    Boolean submitApplyContent(Long id, WenUserPrivilegeApplySubmitReq req);

    /**
     * 触发步骤三（用户B访问申请页面时调用）
     * 
     * @param applyId 申请流程ID
     * @return 是否成功
     */
    Boolean triggerStepThree(Long applyId);

    /**
     * 处理过期的申请流程
     */
    void handleExpiredApplies();

    /**
     * 清理MinIO申请页面资源
     *
     * @param minioPath MinIO资源路径
     */
    void cleanupApplyPage(String minioPath);

    /**
     * 启动申请计时器（用户点击邮件链接访问页面时调用）
     *
     * @param applyId 申请流程ID
     * @return 时间信息
     */
    WenApplyTimeInfoResp startApplyTimer(Long applyId);

    /**
     * 获取申请剩余时间
     *
     * @param applyId 申请流程ID
     * @return 时间信息
     */
    WenApplyTimeInfoResp remainingTime(Long applyId);
}
