<!--
  特权申请流程弹框组件

  功能说明：
  - 使用NaiveUI步骤组件显示申请流程
  - 支持三个步骤的申请流程
  - 实时显示申请状态和剩余时间
  - 支持不同申请类型的处理
-->
<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    title="特权申请"
    :mask-closable="false"
    :closable="!verification.isLoading.value"
    style="width: 600px"
    @close="handleClose"
  >
    <div class="privilege-apply-modal">
      <!-- 步骤指示器 -->
      <NSteps
        :current="verification.currentStep.value"
        :status="getStepStatus()"
        class="apply-steps"
      >
        <NStep
          v-for="step in steps"
          :key="step.value"
          :title="step.title"
          :description="step.description"
        />
      </NSteps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤一：准备申请信息 -->
        <div v-if="verification.currentStep.value === 1" class="step-one">
          <div class="step-title">准备申请信息</div>
          <div class="step-description">
            请访问对应平台链接并准备{{ getVerificationTypeLabel() }}申请信息
          </div>

          <div class="apply-info">
            <div class="info-item">
              <span class="info-label">特权名称：</span>
              <span class="info-value">{{ privilegeName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">申请方式：</span>
              <span class="info-value">{{ getVerificationTypeLabel() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">申请说明：</span>
              <span class="info-value">{{ getVerificationTypeDescription() }}</span>
            </div>
          </div>

          <!-- 模板平台链接 -->
          <div v-if="props.privilege?.link" class="template-link-section">
            <div class="link-title">
              <span class="icon">🔗</span>
              <strong>请先访问对应的模板平台：</strong>
            </div>
            <div class="link-container">
              <NButton
                type="info"
                tag="a"
                :href="props.privilege.link"
                target="_blank"
                @click="handleLinkClick"
                class="template-link-button"
              >
                <template #icon>
                  <span>🌐</span>
                </template>
                访问 {{ privilegeName }} 平台
              </NButton>
            </div>
            <div class="link-description">
              <span v-if="props.privilege?.applyType === 0">
                请在平台上发送验证码，然后返回此页面继续验证流程
              </span>
              <span v-else> 请在平台上截图登录二维码，然后返回此页面继续验证流程 </span>
            </div>
          </div>

          <!-- 二维码图片上传（仅二维码验证类型显示） -->
          <div
            v-if="props.privilege?.applyType === 1 && linkClicked"
            class="qrcode-upload-section"
          >
            <div class="upload-title">
              <span class="icon">📱</span>
              <strong>上传二维码截图：</strong>
            </div>
            <NUpload
              :max="1"
              accept="image/*"
              :show-file-list="false"
              @change="handleQrCodeUpload"
              class="qrcode-upload"
            >
              <NUploadDragger>
                <div v-if="!qrCodeImage" class="upload-placeholder">
                  <div class="upload-icon">📷</div>
                  <div class="upload-text">点击或拖拽上传二维码截图</div>
                  <div class="upload-hint">支持 JPG、PNG 格式</div>
                </div>
                <div v-else-if="isParsingQRCode" class="upload-parsing">
                  <NSpin size="large" />
                  <div class="parsing-text">正在解析二维码...</div>
                </div>
                <div v-else class="upload-preview">
                  <img :src="qrCodeImageUrl" alt="二维码预览" class="qrcode-preview-image" />
                  <div v-if="qrCodeContent" class="upload-success">
                    ✅ 二维码解析成功
                    <div class="qrcode-content">
                      内容: {{ qrCodeContent.substring(0, 50)
                      }}{{ qrCodeContent.length > 50 ? '...' : '' }}
                    </div>
                  </div>
                  <div v-else class="upload-error">❌ 二维码解析失败</div>
                </div>
              </NUploadDragger>
            </NUpload>
          </div>

          <div class="step-actions">
            <NButton @click="handleClose" :disabled="verification.isLoading.value"> 取消 </NButton>
            <NButton
              v-if="shouldShowStartButton"
              type="primary"
              @click="handleStartVerification"
              :loading="verification.isLoading.value"
            >
              发送申请邮件
            </NButton>
            <div v-else class="start-button-hint">
              <span class="hint-icon">💡</span>
              <span v-if="!linkClicked">请先点击上方链接访问模板平台</span>
              <span v-else-if="props.privilege?.applyType === 1 && !qrCodeImage">
                请上传二维码截图后继续
              </span>
              <span
                v-else-if="props.privilege?.applyType === 1 && qrCodeImage && !qrCodeContent"
              >
                请等待二维码解析完成
              </span>
            </div>
          </div>
        </div>

        <!-- 步骤二：发送申请邮件 -->
        <div v-else-if="verification.currentStep.value === 2" class="step-two">
          <div class="step-title">申请邮件已发送</div>
          <div class="step-description">特权申请邮件已发送给提供者，请等待对方在邮件中完成申请</div>

          <!-- 申请页面链接显示 -->
          <div v-if="verification.applyData.value?.pageUrl" class="apply-page-info">
            <div class="info-title">
              <span class="icon">📧</span>
              <strong>申请邮件详情：</strong>
            </div>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">收件人：</span>
                <span class="info-value">特权提供者</span>
              </div>
              <div class="info-item">
                <span class="info-label">申请方式：</span>
                <span class="info-value">{{ getVerificationTypeLabel() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">状态：</span>
                <span class="info-value">等待申请</span>
              </div>
            </div>
          </div>

          <!-- 剩余时间显示 -->
          <div v-if="verification.remainingTime.value > 0" class="remaining-time">
            <NTag type="warning" size="large">
              剩余时间：{{ formatTime(verification.remainingTime.value) }}
            </NTag>
          </div>

          <div class="step-actions">
            <NButton @click="handleClose"> 关闭 </NButton>
          </div>
        </div>

        <!-- 步骤三：等待申请完成 -->
        <div v-else-if="verification.currentStep.value === 3" class="step-three">
          <div class="step-title">等待申请完成</div>
          <div class="step-description">提供者正在邮件中进行申请，请耐心等待</div>

          <!-- 申请状态显示 -->
          <div class="apply-status">
            <div v-if="verification.isInProgress.value" class="status-progress">
              <NSpin size="large" />
              <div class="status-text">申请进行中...</div>
              <div v-if="verification.remainingTime.value > 0" class="remaining-time">
                剩余时间：{{ formatTime(verification.remainingTime.value) }}
              </div>
            </div>

            <div v-else-if="verification.isCompleted.value" class="status-success">
              <div class="status-icon success-icon">✓</div>
              <div class="status-text">申请成功！</div>
              <div class="status-description">特权已成功获取</div>
            </div>

            <div v-else-if="verification.isFailed.value" class="status-failed">
              <div class="status-icon failed-icon">✗</div>
              <div class="status-text">申请失败</div>
              <div class="status-description">请重新尝试申请</div>
            </div>

            <div v-else-if="verification.isTimeout.value" class="status-timeout">
              <div class="status-icon timeout-icon">⏰</div>
              <div class="status-text">申请超时</div>
              <div class="status-description">申请时间已过期</div>
            </div>
          </div>

          <div class="step-actions">
            <NButton
              v-if="
                verification.isCompleted.value ||
                verification.isFailed.value ||
                verification.isTimeout.value
              "
              type="primary"
              @click="handleClose"
            >
              确定
            </NButton>
            <NButton v-else @click="handleClose"> 取消 </NButton>
          </div>
        </div>
      </div>
    </div>
  </NModal>
</template>

<script lang="ts" setup>
import {
  NButton,
  NModal,
  NSpin,
  NStep,
  NSteps,
  NTag,
  NUpload,
  NUploadDragger,
  type UploadFileInfo,
  useMessage,
} from 'naive-ui'
import { computed, ref, watch } from 'vue'

import { usePrivilegeApply } from '@/composables/privilege-apply/usePrivilegeApply'
import { parseQRCodeFromFile } from '@/utils/qrcode/qrcode-parser'
import { APPLY_TYPE_LABELS } from '@/constants/privilege/apply-type.constants'
import {
  APPLY_STEP_DESCRIPTIONS,
  APPLY_STEP_LABELS,
} from '@/constants/privilege-apply/apply-step.constants'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'

interface Props {
  modelValue: boolean
  privilege: PrivilegeResponse | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用申请流程管理
const verification = usePrivilegeApply()
const message = useMessage()

// 弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 移除不再使用的验证内容输入

// 步骤1相关状态
const linkClicked = ref(false)
const qrCodeImage = ref<File | null>(null)
const qrCodeImageUrl = ref('')
const qrCodeContent = ref('')
const isParsingQRCode = ref(false)

// 计算属性
const privilegeName = computed(() => props.privilege?.name || '')

// 是否显示开始验证按钮
const shouldShowStartButton = computed(() => {
  if (!linkClicked.value) return false
  if (props.privilege?.applyType === 1 && (!qrCodeImage.value || !qrCodeContent.value))
    return false
  return true
})

// 步骤配置
const steps = [
  {
    value: 1,
    title: APPLY_STEP_LABELS[1],
    description: APPLY_STEP_DESCRIPTIONS[1],
  },
  {
    value: 2,
    title: APPLY_STEP_LABELS[2],
    description: APPLY_STEP_DESCRIPTIONS[2],
  },
  {
    value: 3,
    title: APPLY_STEP_LABELS[3],
    description: APPLY_STEP_DESCRIPTIONS[3],
  },
]

/**
 * 获取步骤状态
 */
const getStepStatus = () => {
  if (verification.isCompleted.value) return 'finish'
  if (verification.isFailed.value || verification.isTimeout.value) return 'error'
  if (verification.isInProgress.value) return 'process'
  return 'wait'
}

/**
 * 获取申请类型标签
 */
const getVerificationTypeLabel = (): string => {
  if (!props.privilege) return ''

  const type = props.privilege.applyType
  return APPLY_TYPE_LABELS[type as keyof typeof APPLY_TYPE_LABELS] || '未知申请类型'
}

/**
 * 格式化剩余时间
 */
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

/**
 * 获取申请类型描述
 */
const getVerificationTypeDescription = (): string => {
  if (!props.privilege) return ''

  const type = props.privilege.applyType

  if (type === 0) {
    return '通过短信接收申请码进行申请'
  } else if (type === 1) {
    return '通过扫描二维码进行申请'
  } else {
    return '未知申请方式'
  }
}

// 移除不再使用的验证描述和输入框占位符函数

/**
 * 处理链接点击
 */
const handleLinkClick = (): void => {
  linkClicked.value = true
}

/**
 * 处理二维码图片上传
 */
const handleQrCodeUpload = async (options: { fileList: UploadFileInfo[] }): Promise<void> => {
  if (options.fileList.length > 0) {
    const file = options.fileList[0]
    if (file.file) {
      qrCodeImage.value = file.file
      // 创建预览URL
      qrCodeImageUrl.value = URL.createObjectURL(file.file)

      // 解析二维码内容
      isParsingQRCode.value = true
      try {
        const parseResult = await parseQRCodeFromFile(file.file)
        if (parseResult.success && parseResult.data) {
          qrCodeContent.value = parseResult.data
          message.success('二维码解析成功')
        } else {
          qrCodeContent.value = ''
          message.error(parseResult.error || '二维码解析失败')
          // 清除上传的文件
          qrCodeImage.value = null
          if (qrCodeImageUrl.value) {
            URL.revokeObjectURL(qrCodeImageUrl.value)
            qrCodeImageUrl.value = ''
          }
        }
      } catch (error) {
        console.error('解析二维码时发生错误:', error)
        qrCodeContent.value = ''
        message.error('解析二维码时发生错误')
        // 清除上传的文件
        qrCodeImage.value = null
        if (qrCodeImageUrl.value) {
          URL.revokeObjectURL(qrCodeImageUrl.value)
          qrCodeImageUrl.value = ''
        }
      } finally {
        isParsingQRCode.value = false
      }
    }
  } else {
    qrCodeImage.value = null
    qrCodeContent.value = ''
    if (qrCodeImageUrl.value) {
      URL.revokeObjectURL(qrCodeImageUrl.value)
      qrCodeImageUrl.value = ''
    }
  }
}

/**
 * 开始验证流程
 */
const handleStartVerification = async (): Promise<void> => {
  if (!props.privilege) {
    return
  }

  const requestData: any = {
    privilegeId: props.privilege.id,
    applyType: props.privilege.applyType as 0 | 1,
  }

  // 如果是二维码申请，添加qrcc参数
  if (props.privilege.applyType === 1 && qrCodeContent.value) {
    requestData.qrcc = qrCodeContent.value
  }

  const success = await verification.startApply(requestData)

  if (!success) {
    // 申请启动失败，保持在步骤一
    return
  }
}

// 移除不再使用的提交验证内容函数

/**
 * 关闭弹框
 */
const handleClose = (): void => {
  if (verification.isCompleted.value) {
    emit('success')
  }

  // 重置状态
  resetModal()
  visible.value = false
}

/**
 * 重置弹框状态
 */
const resetModal = (): void => {
  linkClicked.value = false
  qrCodeImage.value = null
  qrCodeContent.value = ''
  isParsingQRCode.value = false
  if (qrCodeImageUrl.value) {
    URL.revokeObjectURL(qrCodeImageUrl.value)
    qrCodeImageUrl.value = ''
  }
  verification.reset()
}

// 监听弹框显示状态
watch(visible, (newVisible) => {
  if (!newVisible) {
    // 弹框关闭时重置状态
    resetModal()
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/privilege-apply/privilege-apply-modal';
</style>
