package com.shenmo.wen.app.core.user.pojo.resp;

import lombok.Data;

/**
 * 用户特权申请响应
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeApplyResp {

    /**
     * 申请流程ID
     */
    private Long id;

    /**
     * 申请用户ID
     */
    private Long userId;

    /**
     * 申请的特权ID
     */
    private Long privilegeId;

    /**
     * 特权名称
     */
    private String privilegeName;

    /**
     * 申请类型：0-短信申请，1-二维码申请
     */
    private Integer applyType;

    /**
     * 当前步骤：1,2,3
     */
    private Integer currentStep;

    /**
     * 状态：0-进行中，1-成功，2-失败，3-超时
     */
    private Integer status;

    /**
     * 申请页面URL
     */
    private String pageUrl;

    /**
     * 流程过期时间
     */
    private Long expireTime;

    /**
     * 创建时间
     */
    private Long ctTm;
}
