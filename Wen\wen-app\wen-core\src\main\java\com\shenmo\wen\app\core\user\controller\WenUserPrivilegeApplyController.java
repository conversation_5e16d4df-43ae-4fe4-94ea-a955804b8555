package com.shenmo.wen.app.core.user.controller;

import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplyStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeApplySubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenApplyTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeApplyResp;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeApplyService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户特权申请控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/user-privilege-applies")
@RequiredArgsConstructor
public class WenUserPrivilegeApplyController {

    private final WenUserPrivilegeApplyService applyService;

    /**
     * 创建特权申请流程
     */
    @PostMapping
    public ResponseData<WenUserPrivilegeApplyResp> startApply(
            @Validated @RequestBody WenUserPrivilegeApplyStartReq req) {
        return ResponseData.success(applyService.startApply(req));
    }

    /**
     * 查询申请流程状态
     */
    @GetMapping("/{id}/status")
    public ResponseData<WenUserPrivilegeApplyResp> applyStatus(
            @PathVariable("id") Long id) {
        return ResponseData.success(applyService.applyStatus(id));
    }

    /**
     * 提交申请内容（步骤二）
     */
    @PutMapping("/{id}/content")
    public ResponseData<Boolean> submitApplyContent(
            @PathVariable("id") Long id,
            @Validated @RequestBody WenUserPrivilegeApplySubmitReq req) {
        return ResponseData.success(applyService.submitApplyContent(id, req));
    }

    /**
     * 触发步骤三（用户B访问申请页面时调用）
     */
    @PutMapping("/{id}/step-three")
    public ResponseData<Boolean> triggerStepThree(@PathVariable("id") Long id) {
        return ResponseData.success(applyService.triggerStepThree(id));
    }

    /**
     * 启动申请计时器（用户点击邮件链接访问页面时调用）
     */
    @PostMapping("/{id}/timer")
    public ResponseData<WenApplyTimeInfoResp> startApplyTimer(@PathVariable("id") Long id) {
        return ResponseData.success(applyService.startApplyTimer(id));
    }

    /**
     * 获取申请剩余时间
     */
    @GetMapping("/{id}/remaining-time")
    public ResponseData<WenApplyTimeInfoResp> remainingTime(@PathVariable("id") Long id) {
        return ResponseData.success(applyService.remainingTime(id));
    }
}
