<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信申请 - {subjectPrefix}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 500px;
            width: 100%;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 40px 30px;
        }
        .warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #92400e;
            font-weight: 500;
        }
        .info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #0c4a6e;
        }
        .form-group {
            margin-bottom: 24px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 16px;
        }
        input[type="text"] {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #4f46e5;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            margin-right: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
        }
        .apply-instructions {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
        }
        .apply-instructions h3 {
            margin: 0 0 12px 0;
            color: #0c4a6e;
        }
        .apply-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #0c4a6e;
        }
        .apply-instructions li {
            margin-bottom: 8px;
        }
        .auto-tip {
            margin-top: 12px;
            font-size: 14px;
            color: #64748b;
            font-style: italic;
        }
        .action-buttons {
            text-align: center;
            margin: 32px 0;
        }
        .action-buttons .btn {
            margin: 0 8px;
            min-width: 160px;
        }
        .success-message {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 30px 20px;
            }
            .header {
                padding: 30px 20px;
            }
            .btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 短信申请</h1>
        </div>

        <div class="content">
            <div class="warning">
                <span class="icon">⚠️</span>
                <strong>重要提醒</strong><br>
                请确保您有权限处理此用户的特权申请。未经授权的操作可能涉及隐私泄露。
            </div>

            <div class="info">
                <p><span class="icon">👤</span><strong>申请信息</strong></p>
                <p><strong>申请用户：</strong>{userAUsername}</p>
                <p><strong>特权类型：</strong>{templateName}</p>
            </div>

            <div class="apply-instructions">
                <h3>📋 申请步骤：</h3>
                <ol>
                    <li>等待接收短信验证码</li>
                    <li>在下方输入框中输入收到的验证码</li>
                    <li>点击确认按钮完成申请</li>
                </ol>
                <p class="auto-tip">💡 如不手动输入，系统将在适当时间后自动完成申请</p>
            </div>

            <div class="form-group">
                <label for="smsCode">短信验证码</label>
                <input type="text" id="smsCode" placeholder="请输入6位数字验证码" maxlength="6" />
            </div>

            <div class="action-buttons">
                <button type="button" class="btn btn-primary" id="confirmBtn">
                    ✅ 确认申请
                </button>
                <button type="button" class="btn btn-secondary" onclick="window.close()">
                    ❌ 取消申请
                </button>
            </div>
        </div>
    </div>

    <script>
        let timeCheckInterval = null;
        let applyCompleted = false;
        let timerStarted = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApply();
        });

        async function initializeApply() {
            // 启动计时器
            await startTimer();

            // 开始监控状态
            startStatusMonitoring();

            // 绑定确认按钮
            document.getElementById('confirmBtn').addEventListener('click', confirmApply);
        }

        async function startTimer() {
            try {
                const response = await fetch(`/core/user-privilege-applies/{applyId}/timer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success && result.data) {
                    timerStarted = true;
                    console.log('计时器已启动，剩余时间:', result.data.remainingSeconds, '秒');

                    if (result.data.autoCompleted) {
                        await handleAutoCompletion();
                    }
                } else {
                    console.error('启动计时器失败:', result.message);
                }
            } catch (error) {
                console.error('启动计时器请求失败:', error);
            }
        }

        async function checkTimeStatus() {
            try {
                const response = await fetch(`/core/user-privilege-applies/{applyId}/remaining-time`);
                const result = await response.json();

                if (result.success && result.data) {
                    const timeInfo = result.data;

                    if (timeInfo.autoCompleted) {
                        await handleAutoCompletion();
                        return false;
                    } else if (timeInfo.remainingSeconds <= 0) {
                        console.log('时间已到，等待自动完成...');
                        return true;
                    }

                    console.log('剩余时间:', timeInfo.remainingSeconds, '秒');
                    return true;
                }
            } catch (error) {
                console.error('获取时间状态失败:', error);
            }

            return true;
        }

        function startStatusMonitoring() {
            timeCheckInterval = setInterval(async () => {
                if (!timerStarted || applyCompleted) {
                    return;
                }

                const shouldContinue = await checkTimeStatus();
                if (!shouldContinue) {
                    clearInterval(timeCheckInterval);
                }
            }, 2000);
        }

        async function handleAutoCompletion() {
            if (applyCompleted) return;

            applyCompleted = true;

            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }

            showSuccessMessage('系统已自动完成申请');

            setTimeout(() => {
                window.close();
            }, 2000);
        }

        async function confirmApply() {
            if (applyCompleted) return;

            const smsCode = document.getElementById('smsCode').value.trim();
            if (!smsCode || smsCode.length !== 6 || !/^\d{6}$/.test(smsCode)) {
                alert('请输入6位数字验证码');
                return;
            }

            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }

            const btn = document.getElementById('confirmBtn');
            btn.disabled = true;
            btn.textContent = '🔄 申请确认中...';

            try {
                const response = await fetch(`/core/user-privilege-applies/{applyId}/content`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: smsCode
                    })
                });

                const result = await response.json();

                if (result.success && result.data) {
                    applyCompleted = true;
                    showSuccessMessage('短信申请确认成功！');
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    showErrorMessage(result.message || '申请确认失败，请重试');
                    resetButton();
                }
            } catch (error) {
                console.error('申请确认请求失败:', error);
                showErrorMessage('网络错误，请重试');
                resetButton();
            }
        }

        function resetButton() {
            const btn = document.getElementById('confirmBtn');
            btn.disabled = false;
            btn.textContent = '✅ 确认申请';

            startStatusMonitoring();
        }

        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `
                <div style="background: #d1fae5; border: 1px solid #10b981; color: #065f46;
                            padding: 20px; border-radius: 12px; text-align: center;
                            font-size: 18px; font-weight: 600; margin: 20px 0;">
                    ✅ ${message}，页面即将关闭...
                </div>
            `;

            document.querySelector('.content').appendChild(successDiv);
        }

        function showErrorMessage(message) {
            alert('❌ ' + message);
        }

        window.addEventListener('beforeunload', () => {
            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }
        });
    </script>
</body>
</html>
