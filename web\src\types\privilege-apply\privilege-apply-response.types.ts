/**
 * 用户特权申请响应类型定义
 */
export interface PrivilegeApplyResponse {
  /** 申请流程ID */
  id: number

  /** 申请用户ID */
  userId: number

  /** 申请的特权ID */
  privilegeId: number

  /** 特权名称 */
  privilegeName: string

  /** 申请类型：0-短信申请，1-二维码申请 */
  applyType: 0 | 1

  /** 当前步骤：1,2,3 */
  currentStep: 1 | 2 | 3

  /** 状态：0-进行中，1-成功，2-失败，3-超时 */
  status: 0 | 1 | 2 | 3

  /** 申请页面URL */
  pageUrl: string

  /** 流程过期时间 */
  expireTime: number

  /** 创建时间 */
  ctTm: number
}
