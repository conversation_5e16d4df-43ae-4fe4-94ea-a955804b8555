package com.shenmo.wen.app.core.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.shenmo.wen.app.core.config.properties.ArticleConfigProperties;
import com.shenmo.wen.app.core.config.properties.PrivilegeApplyProperties;
import com.shenmo.wen.modules.user.config.properties.EmailProperties;

/**
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({ ArticleConfigProperties.class, EmailProperties.class,
        PrivilegeApplyProperties.class })
public class PropertiesConfiguration {
}
