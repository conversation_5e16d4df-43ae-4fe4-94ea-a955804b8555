package com.shenmo.wen.app.core.user.pojo.entity;

import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;

import lombok.Data;

/**
 * 用户特权实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName(value = "wen_user_privilege", autoResultMap = true)
public class WenUserPrivilege {

    /**
     * 用户特权ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 特权模板id
     */
    private Long templateId;

    /**
     * 特权过期时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long expireTime;

    /**
     * 特权申请类型：0-短信申请，1-二维码申请
     */
    private Integer applyType;

    /**
     * 特权付费面额
     */
    private Integer paidDenomination;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 修改时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long mdTm;
}
