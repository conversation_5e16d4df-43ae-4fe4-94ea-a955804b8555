package com.shenmo.wen.app.core.user.service.impl;

import com.shenmo.wen.app.core.user.constant.PrivilegeApplyConstant;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeApplyEmailService;
import com.shenmo.wen.common.util.HtmlTemplateUtils;
import com.shenmo.wen.modules.user.config.properties.EmailProperties;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 特权申请邮件服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenUserPrivilegeApplyEmailServiceImpl implements WenUserPrivilegeApplyEmailService {

    private final JavaMailSender mailSender;
    private final EmailProperties emailProperties;

    @Override
    public boolean sendSmsApplyEmail(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(emailProperties.getFrom(), emailProperties.getPersonal());
            helper.setTo(userB.getEmail());

            // 设置邮件主题
            String subject = String.format(PrivilegeApplyConstant.EMAIL_SUBJECT_TEMPLATE,
                    emailProperties.getSubjectPrefix(), template.getName());
            helper.setSubject(subject);

            // 构建邮件内容
            String htmlContent = buildSmsApplyEmailContent(userA, userB, template, pageUrl);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
            log.info("短信申请邮件发送成功，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
            return true;

        } catch (MessagingException e) {
            log.error("短信申请邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        } catch (UnsupportedEncodingException e) {
            log.error("短信申请邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        }
    }

    @Override
    public boolean sendQrCodeApplyEmail(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(emailProperties.getFrom(), emailProperties.getPersonal());
            helper.setTo(userB.getEmail());

            // 设置邮件主题
            String subject = String.format(PrivilegeApplyConstant.EMAIL_SUBJECT_TEMPLATE,
                    emailProperties.getSubjectPrefix(), template.getName());
            helper.setSubject(subject);

            // 构建邮件内容
            String htmlContent = buildQrCodeApplyEmailContent(userA, userB, template, pageUrl);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
            log.info("二维码申请邮件发送成功，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
            return true;

        } catch (MessagingException e) {
            log.error("二维码申请邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        } catch (UnsupportedEncodingException e) {
            log.error("二维码申请邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        }
    }

    /**
     * 构建短信申请邮件内容
     */
    private String buildSmsApplyEmailContent(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("userA", userA);
        variables.put("userB", userB);
        variables.put("template", template);
        variables.put("pageUrl", pageUrl);
        variables.put("content", String.format(PrivilegeApplyConstant.SMS_EMAIL_CONTENT_TEMPLATE,
                userA.getUsername(), template.getName()));

        return HtmlTemplateUtils.processTemplate(PrivilegeApplyConstant.SMS_APPLY_TEMPLATE, variables);
    }

    /**
     * 构建二维码申请邮件内容
     */
    private String buildQrCodeApplyEmailContent(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("userA", userA);
        variables.put("userB", userB);
        variables.put("template", template);
        variables.put("pageUrl", pageUrl);
        variables.put("content", String.format(PrivilegeApplyConstant.QR_CODE_EMAIL_CONTENT_TEMPLATE,
                userA.getUsername(), template.getName()));

        return HtmlTemplateUtils.processTemplate(PrivilegeApplyConstant.QR_CODE_APPLY_TEMPLATE, variables);
    }
}
