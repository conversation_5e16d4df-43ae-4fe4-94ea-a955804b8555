package com.shenmo.wen.app.core.user.constant;

/**
 * 特权申请常量
 * 
 * <AUTHOR>
 */
public interface PrivilegeApplyConstant {

    /**
     * Redis Key前缀
     */
    String REDIS_KEY_PREFIX = "wen:user:privilege:";

    /**
     * 短信申请页面模板名称
     */
    String SMS_APPLY_TEMPLATE = "privilege-apply-sms";

    /**
     * 二维码申请页面模板名称
     */
    String QR_CODE_APPLY_TEMPLATE = "privilege-apply-qrcode";

    /**
     * 邮件主题模板
     */
    String EMAIL_SUBJECT_TEMPLATE = "[%s] 确认申请特权 - %s";

    /**
     * 短信申请邮件内容模板
     */
    String SMS_EMAIL_CONTENT_TEMPLATE = "是否提供给 [%s] 对应的 [%s] 短信验证码等安全警示信息，如果同意请点击下方链接后等待短信验证码并输入";

    /**
     * 二维码申请邮件内容模板
     */
    String QR_CODE_EMAIL_CONTENT_TEMPLATE = "是否同意 [%s] 在对应的 [%s] 登录二维码进行扫描登录等安全警示信息，如果同意点开二维码链接并扫描";

    /**
     * 验证码长度
     */
    int VERIFICATION_CODE_LENGTH = 6;
}
