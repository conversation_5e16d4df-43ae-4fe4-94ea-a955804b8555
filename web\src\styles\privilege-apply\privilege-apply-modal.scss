/*
 * 特权申请模态框样式
 */

.privilege-apply-modal {
  .step-content {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;

    .step-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color-1);
      margin-bottom: 12px;
      text-align: center;
    }

    .step-description {
      font-size: 14px;
      color: var(--text-color-2);
      text-align: center;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .apply-info {
      width: 100%;
      max-width: 400px;
      margin: 0 auto;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color);

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-weight: 500;
          color: var(--text-color-2);
        }

        .value {
          color: var(--text-color-1);
          font-weight: 600;
        }
      }
    }

    .apply-options {
      width: 100%;
      max-width: 500px;
      margin: 0 auto;

      .apply-option {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--primary-color);
          background-color: var(--primary-color-hover);
        }

        &.selected {
          border-color: var(--primary-color);
          background-color: var(--primary-color-pressed);
        }

        .option-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .option-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: var(--primary-color);
          }

          .option-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color-1);
          }
        }

        .option-description {
          font-size: 14px;
          color: var(--text-color-3);
          line-height: 1.5;
          margin-left: 36px;
        }
      }
    }

    .qr-input {
      width: 100%;
      max-width: 400px;
      margin: 16px auto 0;

      .input-label {
        font-size: 14px;
        color: var(--text-color-2);
        margin-bottom: 8px;
        display: block;
      }
    }
  }

  .countdown {
    text-align: center;
    margin: 20px 0;

    .countdown-title {
      font-size: 16px;
      color: var(--text-color-2);
      margin-bottom: 12px;
    }

    .countdown-value {
      font-size: 32px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 8px;
    }

    .countdown-unit {
      font-size: 14px;
      color: var(--text-color-3);
    }
  }

  .status-display {
    text-align: center;
    padding: 40px 20px;

    .status-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;

      &.success {
        background-color: var(--success-color-hover);
        color: var(--success-color);
      }

      &.error {
        background-color: var(--error-color-hover);
        color: var(--error-color);
      }

      &.warning {
        background-color: var(--warning-color-hover);
        color: var(--warning-color);
      }

      &.info {
        background-color: var(--info-color-hover);
        color: var(--info-color);
      }
    }

    .status-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color-1);
      margin-bottom: 8px;
    }

    .status-description {
      font-size: 14px;
      color: var(--text-color-2);
      line-height: 1.6;
    }
  }

  .modal-footer {
    text-align: center;
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
    margin-top: 24px;

    .footer-buttons {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  .progress-bar {
    width: 100%;
    height: 4px;
    background-color: var(--fill-color-light);
    border-radius: 2px;
    overflow: hidden;
    margin: 16px 0;

    .progress-fill {
      height: 100%;
      background-color: var(--primary-color);
      transition: width 0.3s ease;
      border-radius: 2px;
    }
  }

  .apply-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin: 8px 0;

    &:hover {
      color: var(--primary-color-hover);
      text-decoration: underline;
    }

    .link-icon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .privilege-apply-modal {
    .step-content {
      padding: 16px;

      .apply-options {
        .apply-option {
          padding: 12px;

          .option-header {
            .option-title {
              font-size: 14px;
            }
          }

          .option-description {
            font-size: 12px;
            margin-left: 28px;
          }
        }
      }
    }

    .countdown {
      .countdown-value {
        font-size: 24px;
      }
    }

    .status-display {
      padding: 24px 16px;

      .status-icon {
        width: 48px;
        height: 48px;
      }

      .status-title {
        font-size: 18px;
      }
    }
  }
}

// 深色主题适配
[data-theme='dark'] {
  .privilege-apply-modal {
    .step-title {
      color: var(--text-color-1);
    }

    .step-description {
      color: var(--text-color-2);
    }

    .apply-option {
      .option-title {
        color: var(--text-color-1);
      }

      .option-description {
        color: var(--text-color-3);
      }
    }

    .status-title {
      color: var(--text-color-1);
    }

    .status-description {
      color: var(--text-color-2);
    }
  }
}
