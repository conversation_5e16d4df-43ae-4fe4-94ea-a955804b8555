package com.shenmo.wen.app.core.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特权申请类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrivilegeApplyType {

    /**
     * 短信申请
     */
    SMS(0, "短信申请"),

    /**
     * 二维码申请
     */
    QR_CODE(1, "二维码申请");

    private final Integer code;
    private final String description;

    /**
     * 根据类型码获取枚举
     */
    public static PrivilegeApplyType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrivilegeApplyType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
