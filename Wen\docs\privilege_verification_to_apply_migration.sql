-- 用户特权验证系统重构为申请系统的数据库迁移脚本
-- 将 wen_user_privilege_verification 表重命名为 wen_user_privilege_apply

-- 1. 重命名数据表
ALTER TABLE `wen_user_privilege_verification` RENAME TO `wen_user_privilege_apply`;

-- 2. 更新表注释
ALTER TABLE `wen_user_privilege_apply` COMMENT = '用户特权申请流程表';

-- 3. 更新字段注释（如果需要的话）
ALTER TABLE `wen_user_privilege_apply` 
MODIFY COLUMN `id` bigint NOT NULL COMMENT '申请流程ID',
MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '申请用户ID',
MODIFY COLUMN `privilege_id` bigint NOT NULL COMMENT '申请的特权ID',
MODIFY COLUMN `current_step` int NOT NULL COMMENT '当前步骤：1,2,3',
MODIFY COLUMN `status` int NOT NULL COMMENT '状态：0-进行中，1-成功，2-失败，3-超时',
MODIFY COLUMN `page_url` varchar(500) DEFAULT NULL COMMENT '申请页面URL',
MODIFY COLUMN `content` text DEFAULT NULL COMMENT '申请内容(短信申请码/二维码申请内容)',
MODIFY COLUMN `expire_time` timestamp NOT NULL COMMENT '流程过期时间',
MODIFY COLUMN `ct_tm` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
MODIFY COLUMN `page_access_time` timestamp DEFAULT NULL COMMENT '页面访问时间戳（用户点击邮件链接的时间）',
MODIFY COLUMN `auto_complete_time` timestamp DEFAULT NULL COMMENT '自动完成时间戳（页面访问时间+30秒）';

-- 4. 更新用户特权表中的字段名
ALTER TABLE `wen_user_privilege`
CHANGE COLUMN `verification_type` `apply_type` int NOT NULL COMMENT '特权申请类型：0-短信申请，1-二维码申请';

-- 注意：执行此脚本前请确保：
-- 1. 已备份数据库
-- 2. 已停止相关应用服务
-- 3. 确认表结构和数据的完整性
