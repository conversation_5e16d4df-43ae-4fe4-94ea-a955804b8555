{"tools": [{"name": "str-replace-editor", "description": "Tool for editing files.\n* `path` is a file path relative to the workspace root\n* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.\n* Generate `instruction_reminder` first to remind yourself to limit the edits to at most 150 lines.\n\nNotes for using the `str_replace` command:\n* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on\n* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers\n* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE\n* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!\n* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces\n* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file\n* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries\n* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content\n* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.\n\nNotes for using the `insert` command:\n* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on\n* The `insert_line_1` parameter specifies the line number after which to insert the new string\n* The `insert_line_1` parameter is 1-based line number\n* To insert at the very beginning of the file, use `insert_line_1: 0`\n* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.\n\nIMPORTANT:\n* This is the only tool you should use for editing files.\n* If it fails try your best to fix inputs and retry.\n* DO NOT fall back to removing the whole file and recreating it from scratch.\n* DO NOT use sed or any other command line tools for editing files.\n* Try to fit as many edits in one tool call as possible\n* Use the view tool to read files before editing them.", "parameters": {"type": "object", "properties": {"command": {"type": "string", "enum": ["str_replace", "insert"], "description": "The commands to run. Allowed options are: 'str_replace', 'insert'."}, "path": {"type": "string", "description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'."}, "instruction_reminder": {"type": "string", "description": "Reminder to limit edits to at most 150 lines. Should be exactly this string: 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.'"}, "old_str_1": {"type": "string", "description": "Required parameter of `str_replace` command containing the string in `path` to replace."}, "new_str_1": {"type": "string", "description": "Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert."}, "old_str_start_line_number_1": {"type": "integer", "description": "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file."}, "old_str_end_line_number_1": {"type": "integer", "description": "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file."}, "insert_line_1": {"type": "integer", "description": "Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied."}}, "required": ["command", "path", "instruction_reminder"]}}, {"name": "open-browser", "description": "Open a URL in the default browser.\n\n1. The tool takes in a URL and opens it in the default browser.\n2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.\n3. You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL to open in the browser."}}, "required": ["url"]}}, {"name": "diagnostics", "description": "Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}, "description": "Required list of file paths to get issues for from the IDE."}}, "required": ["paths"]}}, {"name": "read-terminal", "description": "Read output from the active or most-recently used VSCode terminal.\n\nBy default, it reads all of the text visible in the terminal, not just the output of the most recent command.\n\nIf you want to read only the selected text in the terminal, set `only_selected=true` in the tool input.\nOnly do this if you know the user has selected text that you want to read.\n\nNote that this is unrelated to the list-processes and read-process tools, which interact with processes that were launched with the \"launch-process\" tool.", "parameters": {"type": "object", "properties": {"only_selected": {"type": "boolean", "description": "Whether to read only the selected text in the terminal."}}, "required": []}}, {"name": "git-commit-retrieval", "description": "This tool is Augment's context engine with git commit history awareness. It:\n1. Takes in a natural language description of the code you are looking for;\n2. Uses the git commit history as the only context for retrieval;\n3. Otherwise functions like the standard codebase-retrieval tool.", "parameters": {"type": "object", "properties": {"information_request": {"type": "string", "description": "A description of the information you need."}}, "required": ["information_request"]}}, {"name": "launch-process", "description": "Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).\n\nIf `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to\n`max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout\nexpires, the process will continue running in the background but the tool call will return. You can then\ninteract with the process using the other process tools.\n\nNote: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`\nwhile another is running, the tool will return an error.\n\nIf `wait=false`, launches a background process in a separate terminal. This returns immediately, while the\nprocess keeps running in the background.\n\nNotes:\n- Use `wait=true` processes when the command is expected to be short, or when you can't\nproceed with your task until the process is complete. Use `wait=false` for processes that are\nexpected to run in the background, such as starting a server you'll need to interact with, or a\nlong-running process that does not need to complete before proceeding with the task.\n- If this tool returns while the process is still running, you can continue to interact with the process\nusing the other available tools. You can wait for the process, read from it, write to it, kill it, etc.\n- You can use this tool to interact with the user's local version control system. Do not use the\nretrieval tool for that purpose.\n- If there is a more specific tool available that can perform the function, use that tool instead of\nthis one.\n\nThe OS is win32. The shell is 'bash'.", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "The shell command to execute."}, "wait": {"type": "boolean", "description": "Whether to wait for the command to complete."}, "max_wait_seconds": {"type": "number", "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed."}, "cwd": {"type": "string", "description": "Required parameter. Absolute path to the working directory for the command."}}, "required": ["command", "wait", "max_wait_seconds", "cwd"]}}, {"name": "kill-process", "description": "Kill a process by its terminal ID.", "parameters": {"type": "object", "properties": {"terminal_id": {"type": "integer", "description": "Terminal ID to kill."}}, "required": ["terminal_id"]}}, {"name": "read-process", "description": "Read output from a terminal.\n\nIf `wait=true` and the process has not yet completed, waits for the terminal to complete up to `max_wait_seconds` seconds before returning its output.\n\nIf `wait=false` or the process has already completed, returns immediately with the current output.", "parameters": {"type": "object", "properties": {"terminal_id": {"type": "integer", "description": "Terminal ID to read from."}, "wait": {"type": "boolean", "description": "Whether to wait for the command to complete."}, "max_wait_seconds": {"type": "number", "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed."}}, "required": ["terminal_id", "wait", "max_wait_seconds"]}}, {"name": "write-process", "description": "Write input to a terminal.", "parameters": {"type": "object", "properties": {"terminal_id": {"type": "integer", "description": "Terminal ID to write to."}, "input_text": {"type": "string", "description": "Text to write to the process's stdin."}}, "required": ["terminal_id", "input_text"]}}, {"name": "list-processes", "description": "List all known terminals created with the launch-process tool and their states.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "web-search", "description": "Search the web for information. Returns results in markdown format.\nEach result includes the URL, title, and a snippet from the page if available.\n\nThis tool uses Google's Custom Search API to find relevant web pages.", "parameters": {"type": "object", "title": "WebSearchInput", "description": "Input schema for the web search tool.", "properties": {"query": {"title": "Query", "description": "The search query to send.", "type": "string"}, "num_results": {"title": "Num Results", "description": "Number of results to return", "default": 5, "minimum": 1, "maximum": 10, "type": "integer"}}, "required": ["query"]}}, {"name": "web-fetch", "description": "Fetches data from a webpage and converts it into Markdown.\n\n1. The tool takes in a URL and returns the content of the page in Markdown format;\n2. If the return is not valid Markdown, it means the tool cannot successfully parse this page.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL to fetch."}}, "required": ["url"]}}, {"name": "codebase-retrieval", "description": "This tool is Augment's context engine, the world's best codebase context engine. It:\n1. Takes in a natural language description of the code you are looking for;\n2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;\n3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;\n4. Can retrieve across different programming languages;\n5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history.", "parameters": {"type": "object", "properties": {"information_request": {"type": "string", "description": "A description of the information you need."}}, "required": ["information_request"]}}, {"name": "remove-files", "description": "Remove files. ONLY use this tool to delete files in the user's workspace. This is the only safe tool to delete files in a way that the user can undo the change. Do NOT use the shell or launch-process tools to remove files.", "parameters": {"type": "object", "properties": {"file_paths": {"type": "array", "items": {"type": "string"}, "description": "The paths of the files to remove."}}, "required": ["file_paths"]}}, {"name": "save-file", "description": "Save a new file. Use this tool to write new files with the attached content. Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely. Use the str-replace-editor tool to edit existing files instead.", "parameters": {"type": "object", "properties": {"instructions_reminder": {"type": "string", "description": "Should be exactly this string: 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.'"}, "path": {"type": "string", "description": "The path of the file to save."}, "file_content": {"type": "string", "description": "The content of the file."}, "add_last_line_newline": {"type": "boolean", "description": "Whether to add a newline at the end of the file (default: true)."}}, "required": ["instructions_reminder", "path", "file_content"]}}, {"name": "view_tasklist", "description": "View the current task list for the conversation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reorganize_tasklist", "description": "Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.", "parameters": {"type": "object", "properties": {"markdown": {"type": "string", "description": "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation."}}, "required": ["markdown"]}}, {"name": "update_tasks", "description": "Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call. Use this on complex sequences of work to plan, track progress, and manage work.", "parameters": {"type": "object", "properties": {"tasks": {"type": "array", "description": "Array of tasks to update. Each task should have a task_id and the properties to update.", "items": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The UUID of the task to update."}, "state": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"], "description": "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x]."}, "name": {"type": "string", "description": "New task name."}, "description": {"type": "string", "description": "New task description."}}, "required": ["task_id"]}}}, "required": ["tasks"]}}, {"name": "add_tasks", "description": "Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work.", "parameters": {"type": "object", "properties": {"tasks": {"type": "array", "description": "Array of tasks to create. Each task should have name and description.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the new task."}, "description": {"type": "string", "description": "The description of the new task."}, "state": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"], "description": "Initial state of the task. Defaults to NOT_STARTED."}, "parent_task_id": {"type": "string", "description": "UUID of the parent task if this should be a subtask."}, "after_task_id": {"type": "string", "description": "UUID of the task after which this task should be inserted."}}, "required": ["name", "description"]}}}, "required": ["tasks"]}}, {"name": "remember", "description": "Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.", "parameters": {"type": "object", "properties": {"memory": {"type": "string", "description": "The concise (1 sentence) memory to remember."}}, "required": ["memory"]}}, {"name": "render-mermaid", "description": "Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality.", "parameters": {"type": "object", "properties": {"diagram_definition": {"type": "string", "description": "The Mermaid diagram definition code to render"}, "title": {"type": "string", "default": "Mermaid Diagram", "description": "Optional title for the diagram"}}, "required": ["diagram_definition"]}}, {"name": "view-range-untruncated", "description": "View a specific range of lines from untruncated content", "parameters": {"type": "object", "properties": {"reference_id": {"type": "string", "description": "The reference ID of the truncated content (found in the truncation footer)"}, "start_line": {"type": "integer", "description": "The starting line number (1-based, inclusive)"}, "end_line": {"type": "integer", "description": "The ending line number (1-based, inclusive)"}}, "required": ["reference_id", "start_line", "end_line"]}}, {"name": "search-untruncated", "description": "Search for a term within untruncated content", "parameters": {"type": "object", "properties": {"reference_id": {"type": "string", "description": "The reference ID of the truncated content (found in the truncation footer)"}, "search_term": {"type": "string", "description": "The term to search for within the content"}, "context_lines": {"type": "integer", "description": "Number of context lines to include before and after matches (default: 2)"}}, "required": ["reference_id", "search_term"]}}, {"name": "view", "description": "Custom tool for viewing files and directories and searching within files with regex query\n* `path` is a file or directory path relative to the workspace root\n* For files: displays the result of applying `cat -n` to the file\n* For directories: lists files and subdirectories up to 2 levels deep\n* If the output is long, it will be truncated and marked with `<response clipped>`\n\nRegex search (for files only):\n* Use `search_query_regex` to search for patterns in the file using regular expressions\n* Use `case_sensitive` parameter to control case sensitivity (default: false)\n* When using regex search, only matching lines and their context will be shown\n* Use `context_lines_before` and `context_lines_after` to control how many lines of context to show (default: 5)\n* Non-matching sections between matches are replaced with `...`\n* If `view_range` is also specified, the search is limited to that range\n\nUse the following regex syntax for `search_query_regex`:\n\n# Regex Syntax Reference\n\nOnly the core regex feature common across JavaScript and Rust are supported.\n\n## Supported regex syntax\n\n* **Escaping** - Escape metacharacters with a backslash: `\\.` `\\+` `\\?` `\\*` `\\|` `\\(` `\\)` `\\[`.\n* **Dot** `.` - matches any character **except newline** (`\\n`, `\\r`, `\\u2028`, `\\u2029`).\n* **Character classes** - `[abc]`, ranges such as `[a-z]`, and negation `[^…]`. Use explicit ASCII ranges; avoid shorthand like `\\d`.\n* **Alternation** - `foo|bar` chooses the leftmost successful branch.\n* **Quantifiers** - `*`, `+`, `?`, `{n}`, `{n,}`, `{n,m}` (greedy). Add `?` after any of these for the lazy version.\n* **Anchors** - `^` (start of line), `$` (end of line).\n* **Special characters** - Use `\\t` for tab character\n\n---\n\n## Do **Not** Use (Unsupported)\n\n* Newline character `\\n`. Only single line mode is supported.\n* Look-ahead / look-behind `(?= … )`, `(?<= … )`.\n* Back-references `\\1`, `\\k<name>`.\n* Groups `(?<name> … )`, `(?P<name> … )`.\n* Shorthand classes `\\d`, `\\s`, `\\w`, `\\b`, Unicode property escapes `\\p{…}`.\n* Flags inside pattern `(?i)`, `(?m)`, etc.\n* Recursion, conditionals, atomic groups, possessive quantifiers\n* Unicode escapes like these `\\u{1F60A}` or `\\u1F60A`.\n\n\nNotes for using the tool:\n* Strongly prefer to use `search_query_regex` instead of `view_range` when looking for a specific symbol in the file.\n* Use the `view_range` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000\n* Indices are 1-based and inclusive\n* Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file\n* The `view_range` and `search_query_regex` parameters are only applicable when viewing files, not directories", "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "Full path to file or directory relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'."}, "type": {"type": "string", "enum": ["file", "directory"], "description": "Type of path to view. Allowed options are: 'file', 'directory'."}, "view_range": {"type": "array", "items": {"type": "integer"}, "description": "Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file."}, "search_query_regex": {"type": "string", "description": "Optional parameter for files only. The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description. When specified, only lines matching the pattern (plus context lines) will be shown. Non-matching sections are replaced with '...'."}, "case_sensitive": {"type": "boolean", "default": false, "description": "Whether the regex search should be case-sensitive. Only used when search_query_regex is specified. Default: false (case-insensitive)."}, "context_lines_before": {"type": "integer", "default": 5, "description": "Number of lines to show before each regex match. Only used when search_query_regex is specified. Default: 5."}, "context_lines_after": {"type": "integer", "default": 5, "description": "Number of lines to show after each regex match. Only used when search_query_regex is specified. Default: 5."}}, "required": ["path", "type"]}}]}