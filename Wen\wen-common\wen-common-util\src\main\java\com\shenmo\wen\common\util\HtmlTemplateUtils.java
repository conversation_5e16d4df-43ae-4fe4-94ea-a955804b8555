package com.shenmo.wen.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTML模板工具类
 * 用于读取和处理HTML模板文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class HtmlTemplateUtils {

    /**
     * 读取HTML模板文件并替换占位符
     * 
     * @param templatePath 模板文件路径（相对于resources目录）
     * @param variables 变量映射，key为占位符名称，value为替换值
     * @return 处理后的HTML内容
     */
    public static String processTemplate(String templatePath, Map<String, Object> variables) {
        try {
            String template = readTemplate(templatePath);
            return replacePlaceholders(template, variables);
        } catch (Exception e) {
            log.error("处理HTML模板失败: {}", templatePath, e);
            return generateErrorTemplate(e.getMessage());
        }
    }

    /**
     * 读取模板文件内容
     * 
     * @param templatePath 模板文件路径
     * @return 模板内容
     * @throws IOException 读取文件异常
     */
    private static String readTemplate(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        if (!resource.exists()) {
            throw new IOException("模板文件不存在: " + templatePath);
        }
        
        try (InputStream inputStream = resource.getInputStream()) {
            return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        }
    }

    /**
     * 替换模板中的占位符
     * 占位符格式：{variableName}
     * 
     * @param template 模板内容
     * @param variables 变量映射
     * @return 替换后的内容
     */
    private static String replacePlaceholders(String template, Map<String, Object> variables) {
        if (variables == null || variables.isEmpty()) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }

    /**
     * 生成错误模板
     * 
     * @param errorMessage 错误信息
     * @return 错误页面HTML
     */
    private static String generateErrorTemplate(String errorMessage) {
        return String.format("""
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>模板加载错误</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            margin: 40px; 
                            background: #f5f5f5; 
                            text-align: center; 
                        }
                        .error-container { 
                            max-width: 500px; 
                            margin: 0 auto; 
                            background: white; 
                            padding: 30px; 
                            border-radius: 8px; 
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
                        }
                        .error-title { 
                            color: #dc3545; 
                            margin-bottom: 20px; 
                        }
                        .error-message { 
                            color: #6c757d; 
                            font-size: 14px; 
                        }
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <h1 class="error-title">模板加载失败</h1>
                        <p class="error-message">%s</p>
                    </div>
                </body>
                </html>
                """, errorMessage);
    }

    /**
     * 邮箱验证码模板常量
     */
    public static class Templates {
        public static final String EMAIL_VERIFICATION = "templates/email-verification.html";
        public static final String PRIVILEGE_VERIFICATION_SMS = "templates/privilege-verification-sms.html";
        public static final String PRIVILEGE_VERIFICATION_QRCODE = "templates/privilege-verification-qrcode.html";
        public static final String SMS_VERIFICATION_PAGE = "templates/sms-verification-page.html";
        public static final String QRCODE_VERIFICATION_PAGE = "templates/qrcode-verification-page.html";
    }
}
